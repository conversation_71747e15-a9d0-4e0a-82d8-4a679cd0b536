{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|public).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|public).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "bF7oDIAQsiDlYBqvLR/WiZdX1cn2FuoutsR3XKrSPcA=", "__NEXT_PREVIEW_MODE_ID": "ed70372c7428de8daeda04f80a0541e9", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "9fe162a60e63fa8d6a7b77e42052536ed1c0ba7eaf1566a353dc8eaf97fc073d", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "931ad41df6a84886a83328b1ffbad582e2eba7a762f505c43610e7facfddaf20"}}}, "sortedMiddleware": ["/"], "functions": {}}